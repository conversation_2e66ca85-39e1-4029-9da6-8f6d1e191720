<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Persona Profile</title>
</head>
<body>
    <h1>Test Persona Profile Data</h1>
    <p><PERSON><PERSON> tombol di bawah untuk menyimpan data persona profile ke localStorage dan navigasi ke result page.</p>
    
    <button onclick="saveTestData()">Simpan Data Test & Buka Result Page</button>
    <button onclick="clearData()">Hapus Data</button>
    
    <script>
        const personaProfileExample = {
            archetype: "The Analytical Innovator",
            shortSummary: "Anda adalah seorang pemikir analitis dengan kecenderungan investigatif yang kuat dan kreativitas tinggi. Kombinasi antara kecerdasan logis-matematis dan keterbukaan terhadap pengalaman baru membuat Anda unggul dalam memecahkan masalah kompleks dengan pendekatan inovatif.",
            strengthSummary: "Kekuatan utama Anda terletak pada analisis mendalam, kreativitas, dan dorongan kuat untuk belajar hal baru. Ini membuat Anda mampu menghasilkan solusi unik di berbagai situasi kompleks.",
            strengths: [
                "Kemampuan analisis yang tajam",
                "Kreativitas dan inovasi",
                "Keingintahuan intelektual yang tinggi",
                "Kemampuan belajar mandiri yang kuat",
                "Pemikiran sistematis dan terstruktur"
            ],
            weaknessSummary: "Anda cenderung overthinking, perfeksionis, dan kadang kurang sabar menghadapi proses lambat atau bekerja sama dengan orang lain.",
            weaknesses: [
                "Terkadang terlalu perfeksionis",
                "Dapat terjebak dalam overthinking",
                "Kurang sabar dengan proses yang lambat",
                "Kemampuan sosial yang perlu dikembangkan",
                "Kesulitan mendelegasikan tugas"
            ],
            careerRecommendation: [
                {
                    careerName: "Data Scientist",
                    careerProspect: {
                        jobAvailability: "high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                },
                {
                    careerName: "Peneliti",
                    careerProspect: {
                        jobAvailability: "moderate",
                        salaryPotential: "moderate",
                        careerProgression: "moderate",
                        industryGrowth: "moderate",
                        skillDevelopment: "high"
                    }
                },
                {
                    careerName: "Pengembang Software",
                    careerProspect: {
                        jobAvailability: "super high",
                        salaryPotential: "high",
                        careerProgression: "high",
                        industryGrowth: "super high",
                        skillDevelopment: "super high"
                    }
                }
            ],
            insights: [
                "Kembangkan keterampilan komunikasi untuk menyampaikan ide kompleks dengan lebih efektif",
                "Latih kemampuan bekerja dalam tim untuk mengimbangi kecenderungan bekerja sendiri",
                "Manfaatkan kekuatan analitis untuk memecahkan masalah sosial",
                "Cari mentor yang dapat membantu mengembangkan keterampilan kepemimpinan",
                "Tetapkan batas waktu untuk menghindari analisis berlebihan"
            ],
            skillSuggestion: [
                "Public Speaking",
                "Leadership",
                "Teamwork",
                "Time Management",
                "Delegation"
            ],
            possiblePitfalls: [
                "Mengisolasi diri dari tim karena terlalu fokus pada analisis individu",
                "Menunda keputusan karena perfeksionisme berlebihan",
                "Kurang membangun jaringan karena terlalu fokus pada teknis"
            ],
            riskTolerance: "moderate",
            workEnvironment: "Lingkungan kerja yang memberikan otonomi intelektual, menghargai inovasi, dan menyediakan tantangan kognitif yang berkelanjutan. Anda berkembang di tempat yang terstruktur namun fleksibel.",
            roleModel: [
                "Marie Curie",
                "Albert Einstein",
                "B.J. Habibie"
            ]
        };

        // Sample assessment results data
        const sampleAssessmentResults = {
            ocean: {
                openness: 85,
                conscientiousness: 78,
                extraversion: 45,
                agreeableness: 62,
                neuroticism: 35
            },
            riasec: {
                realistic: 25,
                investigative: 92,
                artistic: 78,
                social: 45,
                enterprising: 55,
                conventional: 68
            },
            viaIs: {
                creativity: 88,
                curiosity: 92,
                judgment: 85,
                love_of_learning: 90,
                perspective: 82,
                bravery: 65,
                perseverance: 78,
                honesty: 75,
                zest: 58,
                love: 62,
                kindness: 68,
                social_intelligence: 55,
                teamwork: 48,
                fairness: 72,
                leadership: 65,
                forgiveness: 58,
                humility: 62,
                prudence: 85,
                self_regulation: 75,
                appreciation_of_beauty: 82,
                gratitude: 68,
                hope: 72,
                humor: 55,
                spirituality: 45
            }
        };

        function saveTestData() {
            // Save persona profile
            localStorage.setItem('personaProfile', JSON.stringify(personaProfileExample));

            // Save assessment results
            localStorage.setItem('assessmentResults', JSON.stringify(sampleAssessmentResults));

            // Set authentication data
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('userToken', 'dummy-token');
            localStorage.setItem('userData', JSON.stringify({
                email: '<EMAIL>',
                name: 'Test User'
            }));

            alert('Data persona profile dan assessment results berhasil disimpan!');

            // Navigate to result page
            window.location.href = 'http://localhost:5173/#result';
        }

        function clearData() {
            localStorage.removeItem('personaProfile');
            localStorage.removeItem('assessmentResults');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            alert('Data berhasil dihapus!');
        }
    </script>
</body>
</html>
