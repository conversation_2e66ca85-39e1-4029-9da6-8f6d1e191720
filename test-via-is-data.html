<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test VIA-IS Data</title>
</head>
<body>
    <h1>Test VIA-IS Data Setup</h1>
    <p>Click the button below to set up test data and navigate to results page.</p>
    <button onclick="setupTestData()">Setup Test Data & Go to Results</button>

    <script>
        function setupTestData() {
            // Data yang diberikan user
            const testAssessmentResults = {
                "assessmentName": "AI-Driven Talent Mapping",
                "riasec": {
                    "realistic": 75,
                    "investigative": 80,
                    "artistic": 65,
                    "social": 70,
                    "enterprising": 85,
                    "conventional": 60
                },
                "ocean": {
                    "openness": 80,
                    "conscientiousness": 75,
                    "extraversion": 70,
                    "agreeableness": 85,
                    "neuroticism": 40
                },
                "viaIs": {
                    "creativity": 80,
                    "curiosity": 85,
                    "judgment": 75,
                    "loveOfLearning": 90,
                    "perspective": 70,
                    "bravery": 65,
                    "perseverance": 80,
                    "honesty": 85,
                    "zest": 75,
                    "love": 80,
                    "kindness": 85,
                    "socialIntelligence": 75,
                    "teamwork": 80,
                    "fairness": 85,
                    "leadership": 70,
                    "forgiveness": 75,
                    "humility": 80,
                    "prudence": 75,
                    "selfRegulation": 80,
                    "appreciationOfBeauty": 70,
                    "gratitude": 85,
                    "hope": 80,
                    "humor": 75,
                    "spirituality": 60
                }
            };

            // Sample persona profile yang sesuai
            const testPersonaProfile = {
                archetype: "The Balanced Achiever",
                shortSummary: "Anda adalah individu yang seimbang dengan kekuatan dalam pembelajaran, kebaikan, dan pencapaian tujuan. Kombinasi antara cinta belajar yang tinggi, keadilan, dan kemampuan mengatur diri membuat Anda unggul dalam berbagai situasi.",
                strengthSummary: "Kekuatan utama Anda terletak pada pembelajaran berkelanjutan, empati tinggi, dan kemampuan membangun hubungan yang harmonis dengan orang lain.",
                strengths: [
                    "Cinta belajar yang sangat tinggi (90%)",
                    "Kebaikan dan empati yang kuat (85%)",
                    "Keadilan dan integritas tinggi (85%)",
                    "Kemampuan pengaturan diri yang baik (80%)",
                    "Kreativitas dan inovasi (80%)"
                ],
                weaknessSummary: "Area yang dapat dikembangkan meliputi keberanian dalam mengambil risiko dan spiritualitas untuk keseimbangan hidup yang lebih holistik.",
                weaknesses: [
                    "Keberanian dalam situasi sulit perlu ditingkatkan (65%)",
                    "Spiritualitas dan makna hidup perlu eksplorasi lebih dalam (60%)",
                    "Kepemimpinan dapat dikembangkan lebih lanjut (70%)",
                    "Apresiasi keindahan bisa lebih diasah (70%)",
                    "Perspektif wisdom perlu diperdalam (70%)"
                ],
                careerRecommendation: [
                    {
                        careerName: "Educator/Trainer",
                        careerProspect: {
                            jobAvailability: "high",
                            salaryPotential: "moderate",
                            careerProgression: "high",
                            industryGrowth: "high",
                            skillDevelopment: "super high"
                        }
                    },
                    {
                        careerName: "Human Resources Manager",
                        careerProspect: {
                            jobAvailability: "high",
                            salaryPotential: "high",
                            careerProgression: "high",
                            industryGrowth: "moderate",
                            skillDevelopment: "high"
                        }
                    },
                    {
                        careerName: "Social Worker/Counselor",
                        careerProspect: {
                            jobAvailability: "moderate",
                            salaryPotential: "moderate",
                            careerProgression: "moderate",
                            industryGrowth: "high",
                            skillDevelopment: "high"
                        }
                    }
                ],
                insights: [
                    "Manfaatkan cinta belajar Anda untuk terus mengembangkan keterampilan baru",
                    "Gunakan kebaikan dan empati untuk membangun tim yang kuat",
                    "Kembangkan keberanian untuk mengambil peran kepemimpinan",
                    "Eksplorasi spiritualitas untuk keseimbangan hidup yang lebih baik",
                    "Gunakan kreativitas untuk mencari solusi inovatif"
                ],
                skillSuggestion: [
                    "Leadership Development",
                    "Public Speaking",
                    "Conflict Resolution",
                    "Mindfulness & Meditation",
                    "Creative Problem Solving"
                ],
                possiblePitfalls: [
                    "Terlalu fokus pada pembelajaran tanpa implementasi praktis",
                    "Kesulitan membuat keputusan sulit karena terlalu mempertimbangkan perasaan orang lain",
                    "Menghindari konfrontasi yang diperlukan untuk pertumbuhan"
                ],
                riskTolerance: "moderate",
                workEnvironment: "Lingkungan kerja yang mendukung pembelajaran berkelanjutan, kolaborasi tim, dan memberikan kesempatan untuk membantu orang lain berkembang.",
                roleModel: [
                    "Oprah Winfrey",
                    "Nelson Mandela",
                    "Maya Angelou"
                ]
            };

            // Save to localStorage
            localStorage.setItem('assessmentResults', JSON.stringify(testAssessmentResults));
            localStorage.setItem('personaProfile', JSON.stringify(testPersonaProfile));
            localStorage.setItem('assessmentCompleted', 'true');
            localStorage.setItem('assessmentResultReady', 'true');

            alert('Test data has been set up! Redirecting to results page...');
            
            // Redirect to main page and then navigate to results
            window.location.href = 'index.html#result';
        }
    </script>
</body>
</html>
