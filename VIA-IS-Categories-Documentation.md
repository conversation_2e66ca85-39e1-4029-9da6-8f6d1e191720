# VIA-IS Character Strengths - 6 Categories Implementation

## Overview
VIA-IS (Values in Action Inventory of Strengths) mengidentifikasi 24 kekuatan karakter yang dikelompokkan menjadi 6 kategori virtue utama. Implementasi ini menampilkan hasil assessment dalam format yang lebih terstruktur dan mudah dipahami.

## 6 Kategori VIA-IS

### 1. 🧠 Wisdom & Knowledge (Kebijaksanaan dan Pengetahuan)
**Deskripsi:** Kekuatan kognitif yang melibatkan akuisisi dan penggunaan pengetahuan

**Kekuatan yang termasuk:**
- **Creativity (Kreativitas)** - Kemampuan menghasilkan ide-ide baru dan orisinal
- **Curiosity (Keingintahuan)** - Minat yang kuat untuk mengeksplorasi dan belajar
- **Judgment (Penilaian)** - Kemampuan berpikir kritis dan membuat keputusan yang bijak
- **Love of Learning (Cinta <PERSON>)** - Motivasi intrinsik untuk terus belajar dan berkembang
- **Perspective (Perspektif)** - Kemampuan melihat gambaran besar dan memberikan nasihat bijak

### 2. 💪 Courage (Keberanian)
**Deskripsi:** Kekuatan emosional yang melibatkan pelaksanaan kemauan untuk mencapai tujuan

**Kekuatan yang termasuk:**
- **Bravery (Keberanian)** - Kemampuan menghadapi tantangan dan bahaya
- **Perseverance (Ketekunan)** - Kemampuan bertahan dan tidak menyerah
- **Honesty (Kejujuran)** - Autentisitas dan integritas dalam berperilaku
- **Zest (Semangat)** - Energi dan antusiasme dalam menjalani hidup

### 3. ❤️ Humanity (Kemanusiaan)
**Deskripsi:** Kekuatan interpersonal yang melibatkan merawat dan berteman dengan orang lain

**Kekuatan yang termasuk:**
- **Love (Cinta)** - Kemampuan untuk mencintai dan dicintai
- **Kindness (Kebaikan)** - Kemurahan hati dan kepedulian terhadap orang lain
- **Social Intelligence (Kecerdasan Sosial)** - Kemampuan memahami dan berinteraksi dengan orang lain

### 4. ⚖️ Justice (Keadilan)
**Deskripsi:** Kekuatan sipil yang mendasari kehidupan komunitas yang sehat

**Kekuatan yang termasuk:**
- **Teamwork (Kerja Tim)** - Kemampuan bekerja sama dan menjadi bagian dari tim
- **Fairness (Keadilan)** - Kemampuan berlaku adil dan tidak memihak
- **Leadership (Kepemimpinan)** - Kemampuan memimpin dan menginspirasi orang lain

### 5. 🧘 Temperance (Pengendalian Diri)
**Deskripsi:** Kekuatan yang melindungi dari kelebihan dan membantu mencapai keseimbangan

**Kekuatan yang termasuk:**
- **Forgiveness (Pengampunan)** - Kemampuan memaafkan dan tidak mendendam
- **Humility (Kerendahan Hati)** - Kemampuan tidak sombong dan menghargai orang lain
- **Prudence (Kehati-hatian)** - Kemampuan membuat pilihan yang bijak dan hati-hati
- **Self-Regulation (Pengaturan Diri)** - Kemampuan mengontrol emosi dan perilaku

### 6. ✨ Transcendence (Transendensi)
**Deskripsi:** Kekuatan yang menghubungkan dengan alam semesta yang lebih besar dan memberikan makna

**Kekuatan yang termasuk:**
- **Appreciation of Beauty (Apresiasi Keindahan)** - Kemampuan menghargai keindahan dan keunggulan
- **Gratitude (Rasa Syukur)** - Kemampuan menghargai hal-hal baik dalam hidup
- **Hope (Harapan)** - Optimisme dan keyakinan akan masa depan yang baik
- **Humor (Humor)** - Kemampuan melihat sisi lucu dan membawa kegembiraan
- **Spirituality (Spiritualitas)** - Rasa terhubung dengan sesuatu yang lebih besar

## Implementasi Teknis

### Data Structure
```javascript
const VIA_IS_CATEGORIES = {
  'wisdom': {
    name: 'Wisdom & Knowledge',
    description: 'Kekuatan kognitif yang melibatkan akuisisi dan penggunaan pengetahuan',
    icon: '🧠',
    color: 'blue',
    strengths: ['creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective']
  },
  // ... kategori lainnya
};
```

### Fitur yang Diimplementasikan

1. **Kategorisasi Visual**: Setiap kategori ditampilkan dalam card terpisah dengan warna dan ikon yang berbeda
2. **Skor Kategori**: Menghitung rata-rata skor dari semua kekuatan dalam kategori
3. **Progress Bar**: Visualisasi skor kategori dengan progress bar berwarna
4. **Detail Kekuatan**: Menampilkan semua kekuatan dalam kategori beserta skor individual
5. **Responsive Design**: Layout yang responsif untuk desktop dan mobile

### Cara Penggunaan

1. Data VIA-IS harus dalam format camelCase (contoh: `loveOfLearning`, `socialIntelligence`)
2. Skor harus dalam rentang 0-100
3. Fungsi `displayViaIsCategories()` akan otomatis dipanggil saat menampilkan hasil assessment

### Contoh Data Input
```javascript
{
  "viaIs": {
    "creativity": 80,
    "curiosity": 85,
    "judgment": 75,
    "loveOfLearning": 90,
    "perspective": 70,
    // ... 19 kekuatan lainnya
  }
}
```

## Manfaat Kategorisasi

1. **Pemahaman yang Lebih Baik**: User dapat melihat pola kekuatan mereka berdasarkan kategori virtue
2. **Pengembangan Diri yang Terarah**: Dapat fokus mengembangkan kategori tertentu yang skornya rendah
3. **Visualisasi yang Jelas**: Progress bar dan warna membantu interpretasi hasil
4. **Konteks yang Bermakna**: Setiap kategori memiliki penjelasan yang membantu pemahaman

## Rekomendasi Penggunaan

- Gunakan hasil kategorisasi untuk memberikan insight yang lebih mendalam
- Fokus pada kategori dengan skor tertinggi sebagai kekuatan utama
- Identifikasi kategori dengan skor rendah sebagai area pengembangan
- Berikan rekomendasi aktivitas atau karir berdasarkan pola kategori
