# Integration Guide: Assessment Results to Persona Profile

## Overview
Panduan ini menjelaskan bagaimana mengintegrasikan hasil assessment psikometri (RIASEC, OCEAN, VIA-IS) dengan persona profile result page yang telah dibuat.

## Data Flow

```
Assessment Completion → Data Processing → Persona Generation → Result Display
```

## Step 1: Assessment Data Collection

### Expected Data Format
Pastikan assessment menghasilkan data dalam format berikut:

```javascript
const assessmentResults = {
  // RIASEC Holland Codes (0-100 scale)
  riasec: {
    realistic: 25,      // Practical, hands-on
    investigative: 92,  // Analytical, research
    artistic: 78,       // Creative, expressive
    social: 45,         // Helping, teaching
    enterprising: 55,   // Leadership, business
    conventional: 68    // Organized, detail-oriented
  },
  
  // OCEAN Big Five Personality (0-100 scale)
  ocean: {
    openness: 85,           // Openness to experience
    conscientiousness: 78,  // Conscientiousness
    extraversion: 45,       // Extraversion
    agreeableness: 62,      // Agreeableness
    neuroticism: 35         // Neuroticism (lower = more stable)
  },
  
  // VIA-IS Character Strengths (0-100 scale)
  viaIs: {
    // Wisdom & Knowledge
    creativity: 88,
    curiosity: 92,
    judgment: 85,
    love_of_learning: 90,
    perspective: 82,
    
    // Courage
    bravery: 65,
    perseverance: 78,
    honesty: 75,
    zest: 58,
    
    // Humanity
    love: 62,
    kindness: 68,
    social_intelligence: 55,
    
    // Justice
    teamwork: 48,
    fairness: 72,
    leadership: 65,
    
    // Temperance
    forgiveness: 58,
    humility: 62,
    prudence: 85,
    self_regulation: 75,
    
    // Transcendence
    appreciation_of_beauty: 82,
    gratitude: 68,
    hope: 72,
    humor: 55,
    spirituality: 45
  }
};
```

## Step 2: Persona Profile Generation

### Algorithm untuk Generate Persona Profile

```javascript
function generatePersonaProfile(assessmentResults) {
  const { riasec, ocean, viaIs } = assessmentResults;
  
  // 1. Determine Archetype based on highest RIASEC + OCEAN combinations
  const archetype = determineArchetype(riasec, ocean);
  
  // 2. Generate summary based on top traits
  const shortSummary = generateSummary(riasec, ocean, viaIs);
  
  // 3. Extract strengths from top VIA-IS scores
  const strengths = extractTopStrengths(viaIs, 5);
  
  // 4. Identify development areas from lower scores
  const weaknesses = identifyWeaknesses(ocean, viaIs);
  
  // 5. Generate career recommendations based on RIASEC profile
  const careerRecommendation = generateCareerRecommendations(riasec, ocean);
  
  // 6. Create insights based on personality patterns
  const insights = generateInsights(riasec, ocean, viaIs);
  
  // 7. Suggest skills based on weaknesses
  const skillSuggestion = suggestSkills(weaknesses, ocean);
  
  // 8. Identify potential pitfalls
  const possiblePitfalls = identifyPitfalls(ocean, riasec);
  
  // 9. Determine risk tolerance
  const riskTolerance = determineRiskTolerance(ocean);
  
  // 10. Generate work environment preferences
  const workEnvironment = generateWorkEnvironment(riasec, ocean);
  
  // 11. Suggest role models based on archetype
  const roleModel = suggestRoleModels(archetype, riasec);
  
  return {
    archetype,
    shortSummary,
    strengthSummary: generateStrengthSummary(strengths),
    strengths: strengths.map(s => s.displayName),
    weaknessSummary: generateWeaknessSummary(weaknesses),
    weaknesses: weaknesses.map(w => w.displayName),
    careerRecommendation,
    insights,
    skillSuggestion,
    possiblePitfalls,
    riskTolerance,
    workEnvironment,
    roleModel
  };
}
```

## Step 3: Archetype Determination Logic

### Sample Archetype Mapping

```javascript
function determineArchetype(riasec, ocean) {
  const topRiasec = Object.entries(riasec)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 2);
  
  const highOpenness = ocean.openness > 70;
  const highConscientiousness = ocean.conscientiousness > 70;
  const highExtraversion = ocean.extraversion > 70;
  
  // Example logic
  if (riasec.investigative > 80 && highOpenness) {
    return "The Analytical Innovator";
  } else if (riasec.artistic > 80 && highOpenness) {
    return "The Creative Visionary";
  } else if (riasec.enterprising > 80 && highExtraversion) {
    return "The Dynamic Leader";
  } else if (riasec.social > 80 && ocean.agreeableness > 70) {
    return "The Empathetic Connector";
  } else if (riasec.realistic > 80 && highConscientiousness) {
    return "The Practical Builder";
  } else if (riasec.conventional > 80 && highConscientiousness) {
    return "The Systematic Organizer";
  }
  
  return "The Balanced Achiever";
}
```

## Step 4: Career Recommendation Logic

```javascript
function generateCareerRecommendations(riasec, ocean) {
  const careerDatabase = {
    investigative: [
      {
        careerName: "Data Scientist",
        requirements: { investigative: 80, openness: 70 },
        prospects: {
          jobAvailability: "high",
          salaryPotential: "high",
          careerProgression: "high",
          industryGrowth: "super high",
          skillDevelopment: "super high"
        }
      },
      {
        careerName: "Research Scientist",
        requirements: { investigative: 85, conscientiousness: 75 },
        prospects: {
          jobAvailability: "moderate",
          salaryPotential: "high",
          careerProgression: "moderate",
          industryGrowth: "moderate",
          skillDevelopment: "high"
        }
      }
    ],
    // ... other career mappings
  };
  
  // Find matching careers based on RIASEC profile
  const recommendations = [];
  
  Object.entries(riasec).forEach(([type, score]) => {
    if (score > 70 && careerDatabase[type]) {
      careerDatabase[type].forEach(career => {
        const match = calculateCareerMatch(career.requirements, riasec, ocean);
        if (match > 0.7) {
          recommendations.push(career);
        }
      });
    }
  });
  
  return recommendations.slice(0, 3); // Top 3 recommendations
}
```

## Step 5: Integration with Result Page

### Save Data to localStorage

```javascript
// After assessment completion
function saveAssessmentResults(assessmentResults) {
  // Save raw assessment results
  localStorage.setItem('assessmentResults', JSON.stringify(assessmentResults));
  
  // Generate and save persona profile
  const personaProfile = generatePersonaProfile(assessmentResults);
  localStorage.setItem('personaProfile', JSON.stringify(personaProfile));
  
  // Navigate to result page
  navigateTo('result');
}
```

### Update Assessment Service

```javascript
// In src/services/assessmentService.js
export function processAssessmentCompletion(answers) {
  // 1. Calculate scores for each assessment type
  const riasecScores = calculateRiasecScores(answers.riasec);
  const oceanScores = calculateOceanScores(answers.ocean);
  const viaIsScores = calculateViaIsScores(answers.viaIs);
  
  // 2. Combine results
  const assessmentResults = {
    riasec: riasecScores,
    ocean: oceanScores,
    viaIs: viaIsScores
  };
  
  // 3. Generate persona profile
  const personaProfile = generatePersonaProfile(assessmentResults);
  
  // 4. Save both datasets
  saveAssessmentResults(assessmentResults);
  
  return { assessmentResults, personaProfile };
}
```

## Step 6: Testing Integration

### Test Data Generation

```javascript
// Create test data that matches your assessment format
function generateTestData() {
  return {
    riasec: generateRandomScores(['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional']),
    ocean: generateRandomScores(['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']),
    viaIs: generateRandomScores([
      'creativity', 'curiosity', 'judgment', 'love_of_learning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest',
      'love', 'kindness', 'social_intelligence',
      'teamwork', 'fairness', 'leadership',
      'forgiveness', 'humility', 'prudence', 'self_regulation',
      'appreciation_of_beauty', 'gratitude', 'hope', 'humor', 'spirituality'
    ])
  };
}

function generateRandomScores(keys) {
  const scores = {};
  keys.forEach(key => {
    scores[key] = Math.floor(Math.random() * 100);
  });
  return scores;
}
```

## Step 7: Validation

### Data Validation Function

```javascript
function validateAssessmentResults(results) {
  const requiredKeys = {
    riasec: ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'],
    ocean: ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'],
    viaIs: [
      'creativity', 'curiosity', 'judgment', 'love_of_learning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest',
      'love', 'kindness', 'social_intelligence',
      'teamwork', 'fairness', 'leadership',
      'forgiveness', 'humility', 'prudence', 'self_regulation',
      'appreciation_of_beauty', 'gratitude', 'hope', 'humor', 'spirituality'
    ]
  };
  
  // Validate structure and score ranges
  Object.entries(requiredKeys).forEach(([category, keys]) => {
    if (!results[category]) {
      throw new Error(`Missing ${category} data`);
    }
    
    keys.forEach(key => {
      const score = results[category][key];
      if (typeof score !== 'number' || score < 0 || score > 100) {
        throw new Error(`Invalid score for ${category}.${key}: ${score}`);
      }
    });
  });
  
  return true;
}
```

## Implementation Checklist

- [ ] Update assessment scoring algorithms
- [ ] Implement persona generation logic
- [ ] Add data validation
- [ ] Test with sample data
- [ ] Integrate with existing assessment flow
- [ ] Add error handling
- [ ] Test radar chart rendering
- [ ] Validate career recommendations
- [ ] Test responsive design
- [ ] Add loading states
